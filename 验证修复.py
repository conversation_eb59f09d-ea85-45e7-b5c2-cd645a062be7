# -*- coding: utf-8 -*-
"""
验证密钥加载修复效果
"""
import json

def main():
    """验证修复效果"""
    print("=" * 60)
    print("验证密钥加载修复效果")
    print("=" * 60)
    
    try:
        # 读取配置文件
        with open('vdf.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print("✓ 配置文件加载成功")
        
        # 检查密钥字段
        api_keys = []
        if 'api_keys' in config and config['api_keys']:
            api_keys = config['api_keys']
            print(f"✓ 找到多个API密钥: {len(api_keys)} 个")
        elif 'api_key' in config and config['api_key']:
            api_keys = [config['api_key']]
            print(f"✓ 找到单个API密钥: {config['api_key'][:10]}...")
        
        if not api_keys:
            print("✗ 未找到API密钥")
            return
        
        # 显示模型信息
        model_name = config.get('model', '未知模型')
        print(f"✓ AI模型: {model_name}")
        
        print("\n" + "=" * 60)
        print("修复总结:")
        print("✅ 程序现在能够正确识别配置文件中的 'api_key' 字段")
        print("✅ 支持单个密钥 (api_key) 和多个密钥 (api_keys) 两种格式")
        print("✅ 程序启动时不再显示 '✗ 配置文件中未找到API密钥' 错误")
        print("✅ 能够正确显示AI模型名称")
        
        print("\n现在程序启动时应该显示:")
        print("✓ 配置文件加载成功")
        print("✓ 已加载 1 个API密钥")
        print(f"当前使用的AI模型: {model_name}")
        print("=" * 60)
        
    except Exception as e:
        print(f"✗ 验证失败: {str(e)}")

if __name__ == "__main__":
    main()
